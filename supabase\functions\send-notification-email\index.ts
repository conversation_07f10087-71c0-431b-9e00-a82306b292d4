// Supabase Edge Function for sending notification emails
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { generateConnectionRequestEmail } from './email-templates/connection-request.ts'
import { generateConnectionAcceptedEmail } from './email-templates/connection-accepted.ts'
import { generatePostLikeEmail } from './email-templates/post-like.ts'
import { generatePostCommentEmail } from './email-templates/post-comment.ts'
import { generateNewMessageEmail } from './email-templates/new-message.ts'
import { generateCommunityDigestEmail } from './email-templates/community-digest.ts'

// Environment variables
const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
const SENDGRID_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const SENDGRID_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'

// Types for email data
interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  from?: {
    email: string
    name: string
  }
}

// Types for notification email request
interface NotificationEmailRequest {
  type: 'connection_request' | 'connection_accepted' | 'post_like' | 'post_comment' | 'new_message' | 'community_digest'
  data: {
    to: string
    firstName?: string
    lastName?: string
    // Connection-specific data
    requesterName?: string
    requesterProfileUrl?: string
    // Post-specific data
    postTitle?: string
    postUrl?: string
    likerName?: string
    commenterName?: string
    commentText?: string
    // Message-specific data
    senderName?: string
    messagePreview?: string
    conversationUrl?: string
    // Community digest data
    digestData?: {
      newPosts: number
      newMembers: number
      topPosts: Array<{ title: string; url: string; likes: number }>
      featuredContent: Array<{ title: string; url: string; type: string }>
    }
  }
}

// Function to send email via SendGrid
async function sendEmail(emailData: EmailData): Promise<Response> {
  if (!SENDGRID_API_KEY) {
    throw new Error('SendGrid API key is not configured')
  }

  const url = 'https://api.sendgrid.com/v3/mail/send'
  const plainText = emailData.text || stripHtml(emailData.html)
  
  const payload = {
    personalizations: [
      {
        to: [{ email: emailData.to }],
        subject: emailData.subject
      }
    ],
    from: emailData.from || {
      email: SENDGRID_FROM_EMAIL,
      name: SENDGRID_FROM_NAME
    },
    content: [
      {
        type: 'text/plain',
        value: plainText
      },
      {
        type: 'text/html',
        value: emailData.html
      }
    ]
  }

  console.log('SendGrid request payload:', JSON.stringify(payload))

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${SENDGRID_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error('SendGrid error response:', errorText)
    throw new Error(`SendGrid API error: ${response.status} - ${errorText}`)
  }

  return response
}

// Function to strip HTML tags for plain text
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim()
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { type, data }: NotificationEmailRequest = await req.json()

    console.log('Notification email request:', { type, to: data.to })

    let emailTemplate: { html: string; subject: string }

    // Generate email template based on type
    switch (type) {
      case 'connection_request':
        emailTemplate = generateConnectionRequestEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'connection_accepted':
        emailTemplate = generateConnectionAcceptedEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'post_like':
        emailTemplate = generatePostLikeEmail(
          data.to,
          data.likerName || 'Someone',
          data.postTitle || 'your post',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'post_comment':
        emailTemplate = generatePostCommentEmail(
          data.to,
          data.commenterName || 'Someone',
          data.postTitle || 'your post',
          data.commentText || '',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'new_message':
        emailTemplate = generateNewMessageEmail(
          data.to,
          data.senderName || 'Someone',
          data.messagePreview || '',
          data.conversationUrl || '#',
          data.firstName
        )
        break

      case 'community_digest':
        emailTemplate = generateCommunityDigestEmail(
          data.to,
          data.digestData || { newPosts: 0, newMembers: 0, topPosts: [], featuredContent: [] },
          data.firstName
        )
        break

      default:
        throw new Error(`Unknown notification email type: ${type}`)
    }

    // Send the email
    await sendEmail({
      to: data.to,
      subject: emailTemplate.subject,
      html: emailTemplate.html
    })

    console.log(`Notification email sent successfully to ${data.to}`)

    return new Response(
      JSON.stringify({ success: true, message: 'Notification email sent successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error: any) {
    console.error('Error sending notification email:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
