/**
 * Connection request email template
 */

/**
 * Generates a connection request notification email
 * @param email The recipient's email
 * @param requesterName The name of the person who sent the connection request
 * @param requesterProfileUrl The URL to the requester's profile
 * @param firstName Optional first name of the recipient
 * @returns HTML and subject for the email
 */
export function generateConnectionRequestEmail(
  email: string,
  requesterName: string,
  requesterProfileUrl: string,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = `New Connection Request from ${requesterName}`;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        <strong>${requesterName}</strong> would like to connect with you on ZB Innovation Hub!
      </p>

      <p style="margin-bottom: 24px; line-height: 1.5;">
        Building connections is a great way to expand your network, share knowledge, and discover new opportunities within our innovation community.
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${requesterProfileUrl}" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          View Profile & Respond
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5; color: #666;">
        You can accept or decline this connection request by visiting their profile.
      </p>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Helper function to extract name from email
 * @param email The email address
 * @returns Extracted name or null
 */
function extractNameFromEmail(email: string): string | null {
  const localPart = email.split('@')[0];
  
  // Handle common patterns like firstname.lastname or firstname_lastname
  if (localPart.includes('.') || localPart.includes('_')) {
    const parts = localPart.split(/[._]/);
    if (parts.length >= 2) {
      return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    }
  }
  
  // Handle camelCase like firstnameLastname
  const camelCaseMatch = localPart.match(/^([a-z]+)([A-Z][a-z]+)/);
  if (camelCaseMatch) {
    return camelCaseMatch[1].charAt(0).toUpperCase() + camelCaseMatch[1].slice(1);
  }
  
  // If no pattern found, capitalize the first letter
  if (localPart.length > 0) {
    return localPart.charAt(0).toUpperCase() + localPart.slice(1);
  }
  
  return null;
}
