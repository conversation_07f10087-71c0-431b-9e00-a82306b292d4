/**
 * Post comment email template
 */

/**
 * Generates a post comment notification email
 * @param email The recipient's email
 * @param commenterName The name of the person who commented
 * @param postTitle The title or preview of the post
 * @param commentText The comment text
 * @param postUrl The URL to the post
 * @param firstName Optional first name of the recipient
 * @returns HTML and subject for the email
 */
export function generatePostCommentEmail(
  email: string,
  commenterName: string,
  postTitle: string,
  commentText: string,
  postUrl: string,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = `${commenterName} commented on your post`;

  // Truncate post title and comment if too long
  const displayTitle = postTitle.length > 60 ? postTitle.substring(0, 60) + '...' : postTitle;
  const displayComment = commentText.length > 150 ? commentText.substring(0, 150) + '...' : commentText;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        <strong>${commenterName}</strong> commented on your post on ZB Innovation Hub!
      </p>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 24px 0; border-left: 4px solid #0D8A3E;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 8px; font-size: 16px;">Your Post:</h3>
        <p style="margin: 0 0 16px 0; color: #666; font-style: italic;">"${displayTitle}"</p>
        
        <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 14px;">💬 ${commenterName}'s Comment:</h4>
        <p style="margin: 0; color: #333; background-color: white; padding: 12px; border-radius: 4px; border: 1px solid #e0e0e0;">
          "${displayComment}"
        </p>
      </div>

      <p style="margin-bottom: 24px; line-height: 1.5;">
        Comments like this help foster meaningful discussions and build stronger connections within our innovation community. Consider responding to keep the conversation going!
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${postUrl}" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          View Post & Reply
        </a>
      </div>

      <div style="background-color: #f0f8f4; padding: 16px; border-radius: 6px; margin: 24px 0;">
        <p style="margin: 0; line-height: 1.5; color: #0D8A3E; font-weight: bold;">💡 Engagement Tips:</p>
        <ul style="margin: 8px 0 0 0; padding-left: 20px; line-height: 1.5; color: #666; font-size: 14px;">
          <li>Respond thoughtfully to build deeper connections</li>
          <li>Ask follow-up questions to encourage more discussion</li>
          <li>Share additional insights or resources</li>
          <li>Thank commenters for their valuable input</li>
        </ul>
      </div>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Helper function to extract name from email
 * @param email The email address
 * @returns Extracted name or null
 */
function extractNameFromEmail(email: string): string | null {
  const localPart = email.split('@')[0];
  
  // Handle common patterns like firstname.lastname or firstname_lastname
  if (localPart.includes('.') || localPart.includes('_')) {
    const parts = localPart.split(/[._]/);
    if (parts.length >= 2) {
      return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    }
  }
  
  // Handle camelCase like firstnameLastname
  const camelCaseMatch = localPart.match(/^([a-z]+)([A-Z][a-z]+)/);
  if (camelCaseMatch) {
    return camelCaseMatch[1].charAt(0).toUpperCase() + camelCaseMatch[1].slice(1);
  }
  
  // If no pattern found, capitalize the first letter
  if (localPart.length > 0) {
    return localPart.charAt(0).toUpperCase() + localPart.slice(1);
  }
  
  return null;
}
