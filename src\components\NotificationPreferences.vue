<template>
  <div class="notification-preferences">
    <div class="preferences-header">
      <h2>Notification Preferences</h2>
      <p>Manage how you receive notifications from ZB Innovation Hub</p>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading your preferences...</p>
    </div>

    <div v-else class="preferences-content">
      <!-- Global Email Toggle -->
      <div class="preference-section">
        <div class="section-header">
          <h3>Email Notifications</h3>
          <div class="global-toggle">
            <label class="toggle-switch">
              <input 
                type="checkbox" 
                v-model="preferences.email_enabled"
                @change="updatePreferences"
              >
              <span class="toggle-slider"></span>
            </label>
            <span class="toggle-label">
              {{ preferences.email_enabled ? 'Enabled' : 'Disabled' }}
            </span>
          </div>
        </div>
        <p class="section-description">
          Receive email notifications for important activities. You can customize specific types below.
        </p>
      </div>

      <!-- Connection Notifications -->
      <div class="preference-section" :class="{ disabled: !preferences.email_enabled }">
        <h4>Connection Activities</h4>
        
        <div class="preference-item">
          <div class="preference-info">
            <label>Connection Requests</label>
            <p>Get notified when someone wants to connect with you</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.connection_requests"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label>Connection Accepted</label>
            <p>Get notified when someone accepts your connection request</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.connection_accepted"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Post Interactions -->
      <div class="preference-section" :class="{ disabled: !preferences.email_enabled }">
        <h4>Post Interactions</h4>
        
        <div class="preference-item">
          <div class="preference-info">
            <label>Post Likes</label>
            <p>Get notified when someone likes your posts</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.post_likes"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label>Post Comments</label>
            <p>Get notified when someone comments on your posts</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.post_comments"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Messages -->
      <div class="preference-section" :class="{ disabled: !preferences.email_enabled }">
        <h4>Messages</h4>
        
        <div class="preference-item">
          <div class="preference-info">
            <label>New Messages</label>
            <p>Get notified when you receive new direct messages</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.new_messages"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label>Message Digest</label>
            <p>Receive a summary of unread messages</p>
          </div>
          <select 
            v-model="preferences.message_digest_frequency"
            :disabled="!preferences.email_enabled || !preferences.new_messages"
            @change="updatePreferences"
            class="frequency-select"
          >
            <option value="immediate">Immediate</option>
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="never">Never</option>
          </select>
        </div>
      </div>

      <!-- Community Updates -->
      <div class="preference-section" :class="{ disabled: !preferences.email_enabled }">
        <h4>Community Updates</h4>
        
        <div class="preference-item">
          <div class="preference-info">
            <label>Community Digest</label>
            <p>Weekly summary of community activity and highlights</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.community_digest"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label>Featured Content</label>
            <p>Get notified about featured posts and announcements</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.featured_content"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- System Notifications -->
      <div class="preference-section" :class="{ disabled: !preferences.email_enabled }">
        <h4>System & Security</h4>
        
        <div class="preference-item">
          <div class="preference-info">
            <label>Security Alerts</label>
            <p>Important security notifications (recommended)</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.security_alerts"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label>Account Changes</label>
            <p>Notifications about account updates and changes</p>
          </div>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              v-model="preferences.account_changes"
              :disabled="!preferences.email_enabled"
              @change="updatePreferences"
            >
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Save Status -->
      <div v-if="saveStatus" class="save-status" :class="saveStatus.type">
        <span class="status-icon">
          {{ saveStatus.type === 'success' ? '✓' : '⚠' }}
        </span>
        {{ saveStatus.message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { getUserEmailPreferences, updateUserEmailPreferences } from '../services/enhancedNotificationService'

// Store
const authStore = useAuthStore()

// Reactive data
const loading = ref(true)
const saveStatus = ref<{ type: 'success' | 'error', message: string } | null>(null)

// Default preferences
const preferences = ref({
  email_enabled: true,
  connection_requests: true,
  connection_accepted: true,
  post_likes: true,
  post_comments: true,
  new_messages: true,
  message_digest_frequency: 'daily',
  community_digest: true,
  community_digest_frequency: 'weekly',
  featured_content: true,
  security_alerts: true,
  account_changes: true,
  platform_updates: true,
  inactive_reminders: false,
  suggested_connections: false,
  content_recommendations: false
})

// Load user preferences
async function loadPreferences() {
  try {
    loading.value = true
    
    if (!authStore.user?.id) {
      console.error('No user ID available')
      return
    }

    const userPrefs = await getUserEmailPreferences(authStore.user.id)
    
    if (userPrefs) {
      // Merge with defaults
      Object.assign(preferences.value, userPrefs)
    }
  } catch (error) {
    console.error('Error loading preferences:', error)
    showSaveStatus('error', 'Failed to load preferences')
  } finally {
    loading.value = false
  }
}

// Update preferences
async function updatePreferences() {
  try {
    if (!authStore.user?.id) {
      console.error('No user ID available')
      return
    }

    const result = await updateUserEmailPreferences(authStore.user.id, preferences.value)
    
    if (result.success) {
      showSaveStatus('success', 'Preferences saved successfully')
    } else {
      showSaveStatus('error', result.error || 'Failed to save preferences')
    }
  } catch (error) {
    console.error('Error updating preferences:', error)
    showSaveStatus('error', 'Failed to save preferences')
  }
}

// Show save status message
function showSaveStatus(type: 'success' | 'error', message: string) {
  saveStatus.value = { type, message }
  setTimeout(() => {
    saveStatus.value = null
  }, 3000)
}

// Initialize component
onMounted(() => {
  loadPreferences()
})
</script>

<style scoped>
.notification-preferences {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.preferences-header {
  margin-bottom: 32px;
}

.preferences-header h2 {
  color: #0D8A3E;
  margin-bottom: 8px;
}

.preferences-header p {
  color: #666;
  margin: 0;
}

.loading-state {
  text-align: center;
  padding: 48px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0D8A3E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preference-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  transition: opacity 0.3s ease;
}

.preference-section.disabled {
  opacity: 0.5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: #0D8A3E;
}

.global-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preference-section h4 {
  color: #333;
  margin: 0 0 16px 0;
  font-size: 16px;
}

.section-description {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-info {
  flex: 1;
}

.preference-info label {
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.preference-info p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #0D8A3E;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

input:disabled + .toggle-slider {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

.toggle-label {
  font-size: 14px;
  color: #666;
}

.frequency-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333;
}

.frequency-select:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.save-status {
  position: fixed;
  bottom: 24px;
  right: 24px;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.save-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.save-status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-icon {
  font-weight: bold;
}
</style>
