// Supabase Edge Function for processing email notifications
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
const SENDGRID_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const SENDGRID_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

// Types for notification processing
interface NotificationTrigger {
  trigger_table: string
  trigger_operation: string
  notification_type: string
  timestamp: string
  [key: string]: any
}

// Function to send email via SendGrid
async function sendEmail(to: string, subject: string, html: string): Promise<boolean> {
  if (!SENDGRID_API_KEY) {
    console.error('SendGrid API key is not configured')
    return false
  }

  try {
    const url = 'https://api.sendgrid.com/v3/mail/send'
    const plainText = stripHtml(html)
    
    const payload = {
      personalizations: [
        {
          to: [{ email: to }],
          subject: subject
        }
      ],
      from: {
        email: SENDGRID_FROM_EMAIL,
        name: SENDGRID_FROM_NAME
      },
      content: [
        {
          type: 'text/plain',
          value: plainText
        },
        {
          type: 'text/html',
          value: html
        }
      ]
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${SENDGRID_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('SendGrid error response:', errorText)
      return false
    }

    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

// Function to strip HTML tags for plain text
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim()
}

// Function to get user email and preferences
async function getUserEmailInfo(userId: string) {
  try {
    // Get user email from auth.users
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId)
    
    if (authError || !authUser.user?.email) {
      console.error('Error getting user email:', authError)
      return null
    }

    // Get email preferences
    const { data: preferences, error: prefError } = await supabase
      .from('email_notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (prefError) {
      console.log('No email preferences found, using defaults')
      // Create default preferences
      await supabase
        .from('email_notification_preferences')
        .insert({ user_id: userId })
    }

    return {
      email: authUser.user.email,
      preferences: preferences || {
        connection_requests: true,
        connection_accepted: true,
        post_likes: true,
        post_comments: true,
        new_messages: true,
        email_enabled: true
      }
    }
  } catch (error) {
    console.error('Error getting user email info:', error)
    return null
  }
}

// Function to check if notification type is enabled
function isNotificationTypeEnabled(preferences: any, notificationType: string): boolean {
  if (!preferences.email_enabled) return false

  const typeMap: { [key: string]: string } = {
    'connection_request': 'connection_requests',
    'connection_accepted': 'connection_accepted',
    'post_like': 'post_likes',
    'post_comment': 'post_comments',
    'new_message': 'new_messages'
  }

  const prefKey = typeMap[notificationType]
  return prefKey ? preferences[prefKey] : false
}

// Function to process notification and send email
async function processNotification(trigger: NotificationTrigger): Promise<void> {
  try {
    console.log('Processing notification:', trigger)

    let recipientUserId: string | null = null
    let emailData: any = {}

    // Extract recipient and email data based on notification type
    switch (trigger.notification_type) {
      case 'connection_request':
        recipientUserId = trigger.target_user_id
        // Get requester info
        const { data: requesterProfile } = await supabase
          .from('profiles')
          .select('name, profile_url')
          .eq('user_id', trigger.requester_user_id)
          .single()
        
        emailData = {
          requesterName: requesterProfile?.name || 'Someone',
          requesterProfileUrl: requesterProfile?.profile_url || '#'
        }
        break

      case 'connection_accepted':
        recipientUserId = trigger.requester_user_id
        // Get accepter info
        const { data: accepterProfile } = await supabase
          .from('profiles')
          .select('name, profile_url')
          .eq('user_id', trigger.accepter_user_id)
          .single()
        
        emailData = {
          requesterName: accepterProfile?.name || 'Someone',
          requesterProfileUrl: accepterProfile?.profile_url || '#'
        }
        break

      case 'post_like':
        // Get post owner
        const { data: post } = await supabase
          .from('posts')
          .select('user_id, title, content')
          .eq('id', trigger.post_id)
          .single()
        
        if (post) {
          recipientUserId = post.user_id
          // Get liker info
          const { data: likerProfile } = await supabase
            .from('profiles')
            .select('name')
            .eq('user_id', trigger.liker_user_id)
            .single()
          
          emailData = {
            likerName: likerProfile?.name || 'Someone',
            postTitle: post.title || post.content?.substring(0, 50) + '...' || 'your post',
            postUrl: `${Deno.env.get('FRONTEND_URL')}/virtual-community?post=${trigger.post_id}`
          }
        }
        break

      case 'post_comment':
        // Get post owner
        const { data: commentPost } = await supabase
          .from('posts')
          .select('user_id, title, content')
          .eq('id', trigger.post_id)
          .single()
        
        if (commentPost) {
          recipientUserId = commentPost.user_id
          // Get commenter info and comment
          const { data: commenterProfile } = await supabase
            .from('profiles')
            .select('name')
            .eq('user_id', trigger.commenter_user_id)
            .single()
          
          const { data: comment } = await supabase
            .from('comments')
            .select('content')
            .eq('id', trigger.comment_id)
            .single()
          
          emailData = {
            commenterName: commenterProfile?.name || 'Someone',
            postTitle: commentPost.title || commentPost.content?.substring(0, 50) + '...' || 'your post',
            commentText: comment?.content || '',
            postUrl: `${Deno.env.get('FRONTEND_URL')}/virtual-community?post=${trigger.post_id}`
          }
        }
        break

      case 'new_message':
        recipientUserId = trigger.recipient_user_id
        // Get sender info and message preview
        const { data: senderProfile } = await supabase
          .from('profiles')
          .select('name')
          .eq('user_id', trigger.sender_user_id)
          .single()
        
        const { data: message } = await supabase
          .from('user_messages')
          .select('content')
          .eq('id', trigger.message_id)
          .single()
        
        emailData = {
          senderName: senderProfile?.name || 'Someone',
          messagePreview: message?.content?.substring(0, 100) || '',
          conversationUrl: `${Deno.env.get('FRONTEND_URL')}/dashboard/messages`
        }
        break

      default:
        console.log(`No handler for notification type: ${trigger.notification_type}`)
        return
    }

    if (!recipientUserId) {
      console.log('No recipient user ID found')
      return
    }

    // Get user email and preferences
    const userInfo = await getUserEmailInfo(recipientUserId)
    if (!userInfo) {
      console.log('Could not get user email info')
      return
    }

    // Check if this notification type is enabled
    if (!isNotificationTypeEnabled(userInfo.preferences, trigger.notification_type)) {
      console.log(`Email notifications disabled for type: ${trigger.notification_type}`)
      return
    }

    // Send email notification via the notification email function
    const { error } = await supabase.functions.invoke('send-notification-email', {
      body: {
        type: trigger.notification_type,
        data: {
          to: userInfo.email,
          firstName: userInfo.preferences.first_name,
          ...emailData
        }
      }
    })

    if (error) {
      console.error('Error sending notification email:', error)
    } else {
      console.log(`Email notification sent successfully for ${trigger.notification_type}`)
      
      // Log the email
      await supabase
        .from('notification_email_logs')
        .insert({
          user_id: recipientUserId,
          email_address: userInfo.email,
          subject: `Notification: ${trigger.notification_type}`,
          template_name: trigger.notification_type,
          notification_type: trigger.notification_type,
          related_entity_id: trigger.related_entity_id,
          status: 'sent',
          sent_at: new Date().toISOString()
        })
    }

  } catch (error) {
    console.error('Error processing notification:', error)
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { trigger }: { trigger: NotificationTrigger } = await req.json()

    console.log('Received notification trigger:', trigger)

    // Process the notification
    await processNotification(trigger)

    return new Response(
      JSON.stringify({ success: true, message: 'Notification processed successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error: any) {
    console.error('Error processing notification:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
