<template>
  <div class="notification-preferences">
    <div class="preferences-header">
      <h2>Notification Preferences</h2>
      <p>Choose how you'd like to be notified about activities on ZB Innovation Hub</p>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>Loading your preferences...</p>
    </div>

    <form v-else @submit.prevent="savePreferences" class="preferences-form">
      <!-- Global Settings -->
      <div class="preference-section">
        <h3>Global Settings</h3>
        <div class="preference-item">
          <div class="preference-info">
            <label for="email-enabled">Email Notifications</label>
            <p>Receive notifications via email</p>
          </div>
          <div class="preference-control">
            <input
              id="email-enabled"
              v-model="preferences.email_enabled"
              type="checkbox"
              class="toggle-switch"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="push-enabled">Push Notifications</label>
            <p>Receive real-time notifications in your browser</p>
          </div>
          <div class="preference-control">
            <input
              id="push-enabled"
              v-model="preferences.push_enabled"
              type="checkbox"
              class="toggle-switch"
            />
          </div>
        </div>
      </div>

      <!-- Specific Notification Types -->
      <div class="preference-section">
        <h3>Notification Types</h3>
        
        <div class="preference-item">
          <div class="preference-info">
            <label for="connection-requests">Connection Requests</label>
            <p>When someone wants to connect with you</p>
          </div>
          <div class="preference-control">
            <input
              id="connection-requests"
              v-model="preferences.connection_requests"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="messages">Messages</label>
            <p>When you receive a direct message</p>
          </div>
          <div class="preference-control">
            <input
              id="messages"
              v-model="preferences.messages"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="likes">Post Likes</label>
            <p>When someone likes your posts</p>
          </div>
          <div class="preference-control">
            <input
              id="likes"
              v-model="preferences.likes"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="comments">Post Comments</label>
            <p>When someone comments on your posts</p>
          </div>
          <div class="preference-control">
            <input
              id="comments"
              v-model="preferences.comments"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="profile-views">Profile Views</label>
            <p>When someone views your profile</p>
          </div>
          <div class="preference-control">
            <input
              id="profile-views"
              v-model="preferences.profile_views"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="event-reminders">Event Reminders</label>
            <p>Reminders about upcoming events you're interested in</p>
          </div>
          <div class="preference-control">
            <input
              id="event-reminders"
              v-model="preferences.event_reminders"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <label for="system-announcements">System Announcements</label>
            <p>Important updates and announcements from ZB Innovation Hub</p>
          </div>
          <div class="preference-control">
            <input
              id="system-announcements"
              v-model="preferences.system_announcements"
              type="checkbox"
              class="toggle-switch"
              :disabled="!preferences.email_enabled"
            />
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="form-actions">
        <button type="button" @click="resetToDefaults" class="btn-secondary">
          Reset to Defaults
        </button>
        <button type="submit" :disabled="saving" class="btn-primary">
          <span v-if="saving">Saving...</span>
          <span v-else>Save Preferences</span>
        </button>
      </div>
    </form>

    <!-- Success/Error Messages -->
    <div v-if="message" :class="['message', messageType]">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSupabaseClient } from '@/composables/useSupabase'

interface NotificationPreferences {
  email_enabled: boolean
  push_enabled: boolean
  connection_requests: boolean
  likes: boolean
  comments: boolean
  messages: boolean
  profile_views: boolean
  event_reminders: boolean
  system_announcements: boolean
}

const supabase = useSupabaseClient()

const loading = ref(true)
const saving = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

const preferences = ref<NotificationPreferences>({
  email_enabled: true,
  push_enabled: true,
  connection_requests: true,
  likes: true,
  comments: true,
  messages: true,
  profile_views: false,
  event_reminders: true,
  system_announcements: true
})

const defaultPreferences: NotificationPreferences = {
  email_enabled: true,
  push_enabled: true,
  connection_requests: true,
  likes: true,
  comments: true,
  messages: true,
  profile_views: false,
  event_reminders: true,
  system_announcements: true
}

onMounted(async () => {
  await loadPreferences()
})

async function loadPreferences() {
  try {
    loading.value = true
    
    const { data, error } = await supabase.rpc('get_notification_preferences')
    
    if (error) throw error
    
    if (data) {
      preferences.value = {
        email_enabled: data.email_enabled,
        push_enabled: data.push_enabled,
        connection_requests: data.connection_requests,
        likes: data.likes,
        comments: data.comments,
        messages: data.messages,
        profile_views: data.profile_views,
        event_reminders: data.event_reminders,
        system_announcements: data.system_announcements
      }
    }
  } catch (error) {
    console.error('Error loading preferences:', error)
    showMessage('Failed to load preferences', 'error')
  } finally {
    loading.value = false
  }
}

async function savePreferences() {
  try {
    saving.value = true
    
    const { error } = await supabase.rpc('update_notification_preferences', {
      p_email_enabled: preferences.value.email_enabled,
      p_push_enabled: preferences.value.push_enabled,
      p_connection_requests: preferences.value.connection_requests,
      p_likes: preferences.value.likes,
      p_comments: preferences.value.comments,
      p_messages: preferences.value.messages,
      p_profile_views: preferences.value.profile_views,
      p_event_reminders: preferences.value.event_reminders,
      p_system_announcements: preferences.value.system_announcements
    })
    
    if (error) throw error
    
    showMessage('Preferences saved successfully!', 'success')
  } catch (error) {
    console.error('Error saving preferences:', error)
    showMessage('Failed to save preferences', 'error')
  } finally {
    saving.value = false
  }
}

function resetToDefaults() {
  preferences.value = { ...defaultPreferences }
}

function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}
</script>
